import { useState } from 'react'
import ETFSelector from './components/ETFSelector'
import DateSelector from './components/DateSelector'
import DifferenceTable from './components/DifferenceTable'
import SummaryStats from './components/SummaryStats'
import ExportButton from './components/ExportButton'
import { Calendar, TrendingUp, AlertCircle } from 'lucide-react'

function App() {
  const [selectedETF, setSelectedETF] = useState('')
  const [baseDate, setBaseDate] = useState('')
  const [compareDate, setCompareDate] = useState('')
  const [comparisonData, setComparisonData] = useState(null)
  const [loading, setLoading] = useState(false)

  const handleCompare = async () => {
    if (!selectedETF || !baseDate || !compareDate) {
      alert('请选择ETF和两个比较日期')
      return
    }

    setLoading(true)
    // 模拟API调用
    setTimeout(() => {
      setComparisonData(generateMockData())
      setLoading(false)
    }, 1000)
  }

  const generateMockData = () => {
    // 生成更丰富的模拟ETF申赎清单差异数据
    const mockBonds = [
      { code: '110001', name: '21国债⑴', type: '国债' },
      { code: '110002', name: '21国债⑵', type: '国债' },
      { code: '110003', name: '21国债⑶', type: '国债' },
      { code: '110004', name: '21国债⑷', type: '国债' },
      { code: '110005', name: '21国债⑸', type: '国债' },
      { code: '112001', name: '21央行票据01', type: '央行票据' },
      { code: '112002', name: '21央行票据02', type: '央行票据' },
      { code: '113001', name: '平安转债', type: '可转债' },
      { code: '113002', name: '工行转债', type: '可转债' },
      { code: '113003', name: '光大转债', type: '可转债' },
      { code: '120001', name: '21贴债01', type: '贴现债券' },
      { code: '120002', name: '21贴债02', type: '贴现债券' },
      { code: '130001', name: '21地方债01', type: '地方债' },
      { code: '130002', name: '21地方债02', type: '地方债' },
      { code: '130003', name: '21地方债03', type: '地方债' }
    ]

    const differences = mockBonds.map((bond, index) => {
      const scenarios = [
        // 正常持有，无变化
        () => ({
          baseDateQuantity: 1000000 + Math.random() * 2000000,
          compareDateQuantity: 1000000 + Math.random() * 2000000,
          baseDateCashFlag: Math.random() > 0.5 ? '允许' : '必须',
          compareDateCashFlag: Math.random() > 0.5 ? '允许' : '必须',
          baseDateRatio: 0.02 + Math.random() * 0.08
        }),
        // 数量增加
        () => {
          const base = 800000 + Math.random() * 1000000
          return {
            baseDateQuantity: base,
            compareDateQuantity: base + 200000 + Math.random() * 500000,
            baseDateCashFlag: Math.random() > 0.5 ? '允许' : '必须',
            compareDateCashFlag: Math.random() > 0.5 ? '允许' : '必须',
            baseDateRatio: 0.02 + Math.random() * 0.06
          }
        },
        // 数量减少
        () => {
          const base = 1200000 + Math.random() * 1000000
          return {
            baseDateQuantity: base,
            compareDateQuantity: base - 300000 - Math.random() * 400000,
            baseDateCashFlag: Math.random() > 0.5 ? '允许' : '必须',
            compareDateCashFlag: Math.random() > 0.5 ? '允许' : '必须',
            baseDateRatio: 0.03 + Math.random() * 0.07
          }
        },
        // 新增债券
        () => ({
          baseDateQuantity: 0,
          compareDateQuantity: 500000 + Math.random() * 1000000,
          baseDateCashFlag: '-',
          compareDateCashFlag: Math.random() > 0.5 ? '允许' : '必须',
          baseDateRatio: 0,
          isNew: true
        }),
        // 移除债券
        () => ({
          baseDateQuantity: 600000 + Math.random() * 800000,
          compareDateQuantity: 0,
          baseDateCashFlag: Math.random() > 0.5 ? '允许' : '必须',
          compareDateCashFlag: '-',
          baseDateRatio: 0.02 + Math.random() * 0.05,
          isRemoved: true
        })
      ]

      const scenario = scenarios[Math.floor(Math.random() * scenarios.length)]()
      const quantityDiff = scenario.compareDateQuantity - scenario.baseDateQuantity
      const cashFlagChanged = scenario.baseDateCashFlag !== scenario.compareDateCashFlag

      let compareDateRatio
      if (scenario.isNew) {
        compareDateRatio = 0.01 + Math.random() * 0.04
      } else if (scenario.isRemoved) {
        compareDateRatio = 0
      } else {
        compareDateRatio = scenario.baseDateRatio + (Math.random() - 0.5) * 0.02
      }

      const ratioChanged = Math.abs(scenario.baseDateRatio - compareDateRatio) > 0.001

      return {
        id: index + 1,
        bondCode: bond.code,
        bondName: bond.name,
        bondType: bond.type,
        baseDateQuantity: Math.round(scenario.baseDateQuantity),
        compareDateQuantity: Math.round(scenario.compareDateQuantity),
        quantityDiff: Math.round(quantityDiff),
        baseDateCashFlag: scenario.baseDateCashFlag,
        compareDateCashFlag: scenario.compareDateCashFlag,
        cashFlagChanged,
        baseDateRatio: scenario.baseDateRatio,
        compareDateRatio,
        ratioChanged,
        isNew: scenario.isNew || false,
        isRemoved: scenario.isRemoved || false
      }
    })

    return {
      etfName: selectedETF,
      baseDate,
      compareDate,
      differences
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <TrendingUp className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">ETF申赎清单差异比较</h1>
          </div>
          <p className="text-gray-600">选择ETF和日期，对比分析申赎清单的变化情况</p>
        </div>

        {/* 选择器区域 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <ETFSelector
              selectedETF={selectedETF}
              onETFChange={setSelectedETF}
            />
            <DateSelector
              label="基准日期"
              value={baseDate}
              onChange={setBaseDate}
              icon={<Calendar className="h-5 w-5" />}
            />
            <DateSelector
              label="对比日期"
              value={compareDate}
              onChange={setCompareDate}
              icon={<Calendar className="h-5 w-5" />}
            />
          </div>

          <div className="mt-6 flex justify-center">
            <button
              onClick={handleCompare}
              disabled={loading || !selectedETF || !baseDate || !compareDate}
              className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200 font-medium"
            >
              {loading ? '分析中...' : '开始比较'}
            </button>
          </div>
        </div>

        {/* 结果展示区域 */}
        {comparisonData && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">差异分析结果</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    {comparisonData.etfName} | {comparisonData.baseDate} vs {comparisonData.compareDate}
                  </p>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 text-amber-600">
                    <AlertCircle className="h-5 w-5" />
                    <span className="text-sm font-medium">
                      发现 {comparisonData.differences.filter(d => d.quantityDiff !== 0 || d.cashFlagChanged || d.ratioChanged).length} 项差异
                    </span>
                  </div>
                  <ExportButton
                    data={comparisonData.differences}
                    etfName={comparisonData.etfName}
                    baseDate={comparisonData.baseDate}
                    compareDate={comparisonData.compareDate}
                  />
                </div>
              </div>
            </div>

            <div className="p-6 border-b border-gray-200">
              <SummaryStats data={comparisonData.differences} />
            </div>

            <DifferenceTable data={comparisonData.differences} />
          </div>
        )}
      </div>
    </div>
  )
}

export default App
