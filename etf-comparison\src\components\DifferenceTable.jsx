import { useState } from 'react'
import { ArrowUpDown, Plus, <PERSON>us, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, XCircle } from 'lucide-react'

const DifferenceTable = ({ data }) => {
  const [sortField, setSortField] = useState('')
  const [sortDirection, setSortDirection] = useState('asc')
  const [filterType, setFilterType] = useState('all')

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const filteredAndSortedData = () => {
    let filtered = data

    // 筛选逻辑
    if (filterType === 'changed') {
      filtered = data.filter(item => 
        item.quantityDiff !== 0 || item.cashFlagChanged || item.ratioChanged
      )
    } else if (filterType === 'new') {
      filtered = data.filter(item => item.isNew)
    } else if (filterType === 'removed') {
      filtered = data.filter(item => item.isRemoved)
    }

    // 排序逻辑
    if (sortField) {
      filtered.sort((a, b) => {
        let aVal = a[sortField]
        let bVal = b[sortField]
        
        if (typeof aVal === 'string') {
          aVal = aVal.toLowerCase()
          bVal = bVal.toLowerCase()
        }
        
        if (sortDirection === 'asc') {
          return aVal > bVal ? 1 : -1
        } else {
          return aVal < bVal ? 1 : -1
        }
      })
    }

    return filtered
  }

  const formatNumber = (num) => {
    return new Intl.NumberFormat('zh-CN').format(num)
  }

  const formatPercentage = (num) => {
    return (num * 100).toFixed(2) + '%'
  }

  const getDifferenceIcon = (diff) => {
    if (diff > 0) return <Plus className="h-4 w-4 text-green-600" />
    if (diff < 0) return <Minus className="h-4 w-4 text-red-600" />
    return <CheckCircle className="h-4 w-4 text-gray-400" />
  }

  const getDifferenceColor = (diff) => {
    if (diff > 0) return 'text-green-600 bg-green-50'
    if (diff < 0) return 'text-red-600 bg-red-50'
    return 'text-gray-600 bg-gray-50'
  }

  const getRowClassName = (item) => {
    if (item.isNew) return 'bg-green-50 border-l-4 border-green-400'
    if (item.isRemoved) return 'bg-red-50 border-l-4 border-red-400'
    if (item.quantityDiff !== 0 || item.cashFlagChanged || item.ratioChanged) {
      return 'bg-yellow-50 border-l-4 border-yellow-400'
    }
    return 'bg-white'
  }

  return (
    <div className="p-6">
      {/* 筛选器 */}
      <div className="mb-6 flex flex-wrap gap-2">
        <button
          onClick={() => setFilterType('all')}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filterType === 'all' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          全部 ({data.length})
        </button>
        <button
          onClick={() => setFilterType('changed')}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filterType === 'changed' 
              ? 'bg-yellow-600 text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          有变化 ({data.filter(item => item.quantityDiff !== 0 || item.cashFlagChanged || item.ratioChanged).length})
        </button>
        <button
          onClick={() => setFilterType('new')}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filterType === 'new' 
              ? 'bg-green-600 text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          新增 ({data.filter(item => item.isNew).length})
        </button>
        <button
          onClick={() => setFilterType('removed')}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
            filterType === 'removed' 
              ? 'bg-red-600 text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          移除 ({data.filter(item => item.isRemoved).length})
        </button>
      </div>

      {/* 表格 */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('bondCode')}
              >
                <div className="flex items-center space-x-1">
                  <span>债券代码</span>
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('bondName')}
              >
                <div className="flex items-center space-x-1">
                  <span>债券名称</span>
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                债券类型
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                基准日期数量
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                对比日期数量
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('quantityDiff')}
              >
                <div className="flex items-center space-x-1">
                  <span>数量差异</span>
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                现金替代标志
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                权重比例
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredAndSortedData().map((item) => (
              <tr key={item.id} className={getRowClassName(item)}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2">
                    {item.isNew && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <Plus className="h-3 w-3 mr-1" />
                        新增
                      </span>
                    )}
                    {item.isRemoved && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <XCircle className="h-3 w-3 mr-1" />
                        移除
                      </span>
                    )}
                    {!item.isNew && !item.isRemoved && (item.quantityDiff !== 0 || item.cashFlagChanged || item.ratioChanged) && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        变化
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {item.bondCode}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {item.bondName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    {item.bondType || '债券'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {item.baseDateQuantity > 0 ? formatNumber(item.baseDateQuantity) : '-'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {item.compareDateQuantity > 0 ? formatNumber(item.compareDateQuantity) : '-'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-md text-sm font-medium ${getDifferenceColor(item.quantityDiff)}`}>
                    {getDifferenceIcon(item.quantityDiff)}
                    <span>{item.quantityDiff !== 0 ? formatNumber(Math.abs(item.quantityDiff)) : '无变化'}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-600">基准:</span>
                      <span className={item.cashFlagChanged ? 'line-through text-gray-400' : 'text-gray-900'}>
                        {item.baseDateCashFlag}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-600">对比:</span>
                      <span className={`${item.cashFlagChanged ? 'font-medium text-blue-600' : 'text-gray-900'}`}>
                        {item.compareDateCashFlag}
                      </span>
                      {item.cashFlagChanged && (
                        <AlertTriangle className="h-4 w-4 text-amber-500" />
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-600">基准:</span>
                      <span className={item.ratioChanged ? 'line-through text-gray-400' : 'text-gray-900'}>
                        {formatPercentage(item.baseDateRatio)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-600">对比:</span>
                      <span className={`${item.ratioChanged ? 'font-medium text-blue-600' : 'text-gray-900'}`}>
                        {formatPercentage(item.compareDateRatio)}
                      </span>
                      {item.ratioChanged && (
                        <AlertTriangle className="h-4 w-4 text-amber-500" />
                      )}
                    </div>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredAndSortedData().length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg">没有找到符合条件的数据</div>
        </div>
      )}
    </div>
  )
}

export default DifferenceTable
