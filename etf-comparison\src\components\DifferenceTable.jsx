import { useState } from 'react'
import { ArrowUpDown, Plus, <PERSON>us, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, XCircle } from 'lucide-react'

const DifferenceTable = ({ data }) => {
  const [sortField, setSortField] = useState('')
  const [sortDirection, setSortDirection] = useState('asc')
  const [filterType, setFilterType] = useState('all')

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const filteredAndSortedData = () => {
    let filtered = data

    // 筛选逻辑
    if (filterType === 'changed') {
      filtered = data.filter(item => 
        item.quantityDiff !== 0 || item.cashFlagChanged || item.ratioChanged
      )
    } else if (filterType === 'new') {
      filtered = data.filter(item => item.isNew)
    } else if (filterType === 'removed') {
      filtered = data.filter(item => item.isRemoved)
    }

    // 排序逻辑
    if (sortField) {
      filtered.sort((a, b) => {
        let aVal = a[sortField]
        let bVal = b[sortField]
        
        if (typeof aVal === 'string') {
          aVal = aVal.toLowerCase()
          bVal = bVal.toLowerCase()
        }
        
        if (sortDirection === 'asc') {
          return aVal > bVal ? 1 : -1
        } else {
          return aVal < bVal ? 1 : -1
        }
      })
    }

    return filtered
  }

  const formatNumber = (num) => {
    return new Intl.NumberFormat('zh-CN').format(num)
  }

  const formatPercentage = (num) => {
    return (num * 100).toFixed(2) + '%'
  }

  const getDifferenceIcon = (diff) => {
    if (diff > 0) return <Plus className="h-4 w-4 text-green-600" />
    if (diff < 0) return <Minus className="h-4 w-4 text-red-600" />
    return <CheckCircle className="h-4 w-4 text-gray-400" />
  }

  const getDifferenceClass = (diff) => {
    if (diff > 0) return 'positive'
    if (diff < 0) return 'negative'
    return 'neutral'
  }

  const getRowClassName = (item) => {
    if (item.isNew) return 'new'
    if (item.isRemoved) return 'removed'
    if (item.quantityDiff !== 0 || item.cashFlagChanged || item.ratioChanged) {
      return 'changed'
    }
    return 'normal'
  }

  return (
    <div style={{ padding: '1.5rem' }}>
      {/* 筛选器 */}
      <div className="filter-buttons">
        <button
          onClick={() => setFilterType('all')}
          className={`filter-btn all ${filterType === 'all' ? 'active' : 'inactive'}`}
        >
          全部 ({data.length})
        </button>
        <button
          onClick={() => setFilterType('changed')}
          className={`filter-btn changed ${filterType === 'changed' ? 'active' : 'inactive'}`}
        >
          有变化 ({data.filter(item => item.quantityDiff !== 0 || item.cashFlagChanged || item.ratioChanged).length})
        </button>
        <button
          onClick={() => setFilterType('new')}
          className={`filter-btn new ${filterType === 'new' ? 'active' : 'inactive'}`}
        >
          新增 ({data.filter(item => item.isNew).length})
        </button>
        <button
          onClick={() => setFilterType('removed')}
          className={`filter-btn removed ${filterType === 'removed' ? 'active' : 'inactive'}`}
        >
          移除 ({data.filter(item => item.isRemoved).length})
        </button>
      </div>

      {/* 表格 */}
      <div className="table-container">
        <table className="table">
          <thead>
            <tr>
              <th>状态</th>
              <th
                className="sortable"
                onClick={() => handleSort('bondCode')}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                  <span>债券代码</span>
                  <ArrowUpDown className="icon" />
                </div>
              </th>
              <th
                className="sortable"
                onClick={() => handleSort('bondName')}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                  <span>债券名称</span>
                  <ArrowUpDown className="icon" />
                </div>
              </th>
              <th>债券类型</th>
              <th>基准日期数量</th>
              <th>对比日期数量</th>
              <th
                className="sortable"
                onClick={() => handleSort('quantityDiff')}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                  <span>数量差异</span>
                  <ArrowUpDown className="icon" />
                </div>
              </th>
              <th>现金替代标志</th>
              <th>权重比例</th>
            </tr>
          </thead>
          <tbody>
            {filteredAndSortedData().map((item) => (
              <tr key={item.id} className={getRowClassName(item)}>
                <td>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    {item.isNew && (
                      <span className="status-badge new">
                        <Plus className="icon" style={{ width: '0.75rem', height: '0.75rem' }} />
                        新增
                      </span>
                    )}
                    {item.isRemoved && (
                      <span className="status-badge removed">
                        <XCircle className="icon" style={{ width: '0.75rem', height: '0.75rem' }} />
                        移除
                      </span>
                    )}
                    {!item.isNew && !item.isRemoved && (item.quantityDiff !== 0 || item.cashFlagChanged || item.ratioChanged) && (
                      <span className="status-badge changed">
                        <AlertTriangle className="icon" style={{ width: '0.75rem', height: '0.75rem' }} />
                        变化
                      </span>
                    )}
                  </div>
                </td>
                <td style={{ fontWeight: '500' }}>
                  {item.bondCode}
                </td>
                <td>
                  {item.bondName}
                </td>
                <td>
                  <span className="status-badge bond-type">
                    {item.bondType || '债券'}
                  </span>
                </td>
                <td>
                  {item.baseDateQuantity > 0 ? formatNumber(item.baseDateQuantity) : '-'}
                </td>
                <td>
                  {item.compareDateQuantity > 0 ? formatNumber(item.compareDateQuantity) : '-'}
                </td>
                <td>
                  <div className={`diff-container ${getDifferenceClass(item.quantityDiff)}`}>
                    {getDifferenceIcon(item.quantityDiff)}
                    <span>{item.quantityDiff !== 0 ? formatNumber(Math.abs(item.quantityDiff)) : '无变化'}</span>
                  </div>
                </td>
                <td>
                  <div className="compare-field">
                    <div className="compare-row">
                      <span className="compare-label">基准:</span>
                      <span className={`compare-value ${item.cashFlagChanged ? 'old' : ''}`}>
                        {item.baseDateCashFlag}
                      </span>
                    </div>
                    <div className="compare-row">
                      <span className="compare-label">对比:</span>
                      <span className={`compare-value ${item.cashFlagChanged ? 'new' : ''}`}>
                        {item.compareDateCashFlag}
                      </span>
                      {item.cashFlagChanged && (
                        <AlertTriangle className="icon" style={{ color: '#d97706' }} />
                      )}
                    </div>
                  </div>
                </td>
                <td>
                  <div className="compare-field">
                    <div className="compare-row">
                      <span className="compare-label">基准:</span>
                      <span className={`compare-value ${item.ratioChanged ? 'old' : ''}`}>
                        {formatPercentage(item.baseDateRatio)}
                      </span>
                    </div>
                    <div className="compare-row">
                      <span className="compare-label">对比:</span>
                      <span className={`compare-value ${item.ratioChanged ? 'new' : ''}`}>
                        {formatPercentage(item.compareDateRatio)}
                      </span>
                      {item.ratioChanged && (
                        <AlertTriangle className="icon" style={{ color: '#d97706' }} />
                      )}
                    </div>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredAndSortedData().length === 0 && (
        <div className="empty-state">
          <div>没有找到符合条件的数据</div>
        </div>
      )}
    </div>
  )
}

export default DifferenceTable
