import { TrendingUp } from 'lucide-react'

const ETFSelector = ({ selectedETF, onETFChange }) => {
  const etfOptions = [
    { code: '511010', name: '国债ETF', type: '债券ETF' },
    { code: '511020', name: '活跃债券ETF', type: '债券ETF' },
    { code: '511030', name: '信用债ETF', type: '债券ETF' },
    { code: '511220', name: '城投债ETF', type: '债券ETF' },
    { code: '511260', name: '十年国债ETF', type: '债券ETF' },
    { code: '511270', name: '十年地方债ETF', type: '债券ETF' },
    { code: '511280', name: '五年国债ETF', type: '债券ETF' },
    { code: '511290', name: '三年国债ETF', type: '债券ETF' }
  ]

  return (
    <div className="space-y-2">
      <label className="flex items-center space-x-2 text-sm font-medium text-gray-700">
        <TrendingUp className="h-4 w-4" />
        <span>选择ETF</span>
      </label>
      <select
        value={selectedETF}
        onChange={(e) => onETFChange(e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        <option value="">请选择ETF</option>
        {etfOptions.map((etf) => (
          <option key={etf.code} value={`${etf.code} - ${etf.name}`}>
            {etf.code} - {etf.name} ({etf.type})
          </option>
        ))}
      </select>
    </div>
  )
}

export default ETFSelector
