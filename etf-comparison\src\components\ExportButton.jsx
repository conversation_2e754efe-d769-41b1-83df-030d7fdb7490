import { Download } from 'lucide-react'

const ExportButton = ({ data, etfName, baseDate, compareDate }) => {
  const exportToCSV = () => {
    const headers = [
      '状态',
      '债券代码',
      '债券名称',
      '债券类型',
      '基准日期数量',
      '对比日期数量',
      '数量差异',
      '基准日期现金替代标志',
      '对比日期现金替代标志',
      '现金替代标志是否变化',
      '基准日期权重比例',
      '对比日期权重比例',
      '权重比例是否变化'
    ]

    const csvContent = [
      headers.join(','),
      ...data.map(item => [
        item.isNew ? '新增' : item.isRemoved ? '移除' : '正常',
        item.bondCode,
        `"${item.bondName}"`,
        item.bondType || '债券',
        item.baseDateQuantity,
        item.compareDateQuantity,
        item.quantityDiff,
        item.baseDateCashFlag,
        item.compareDateCashFlag,
        item.cashFlagChanged ? '是' : '否',
        (item.baseDateRatio * 100).toFixed(4) + '%',
        (item.compareDateRatio * 100).toFixed(4) + '%',
        item.ratioChanged ? '是' : '否'
      ].join(','))
    ].join('\n')

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `ETF申赎清单差异_${etfName}_${baseDate}_vs_${compareDate}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <button
      onClick={exportToCSV}
      className="btn btn-secondary"
      style={{ display: 'inline-flex', alignItems: 'center', gap: '0.5rem' }}
    >
      <Download className="icon" />
      导出CSV
    </button>
  )
}

export default ExportButton
