import { TrendingUp, TrendingDown, Plus, Minus, <PERSON><PERSON><PERSON>riangle } from 'lucide-react'

const SummaryStats = ({ data }) => {
  const stats = {
    total: data.length,
    changed: data.filter(item => item.quantityDiff !== 0 || item.cashFlagChanged || item.ratioChanged).length,
    new: data.filter(item => item.isNew).length,
    removed: data.filter(item => item.isRemoved).length,
    increased: data.filter(item => item.quantityDiff > 0).length,
    decreased: data.filter(item => item.quantityDiff < 0).length,
    cashFlagChanged: data.filter(item => item.cashFlagChanged).length,
    ratioChanged: data.filter(item => item.ratioChanged).length
  }

  const StatCard = ({ title, value, icon, color, bgColor }) => (
    <div className={`${bgColor} rounded-lg p-4 border border-gray-200`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-bold ${color}`}>{value}</p>
        </div>
        <div className={`${color}`}>
          {icon}
        </div>
      </div>
    </div>
  )

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-6">
      <StatCard
        title="总数"
        value={stats.total}
        icon={<TrendingUp className="h-6 w-6" />}
        color="text-blue-600"
        bgColor="bg-blue-50"
      />
      <StatCard
        title="有变化"
        value={stats.changed}
        icon={<AlertTriangle className="h-6 w-6" />}
        color="text-yellow-600"
        bgColor="bg-yellow-50"
      />
      <StatCard
        title="新增"
        value={stats.new}
        icon={<Plus className="h-6 w-6" />}
        color="text-green-600"
        bgColor="bg-green-50"
      />
      <StatCard
        title="移除"
        value={stats.removed}
        icon={<Minus className="h-6 w-6" />}
        color="text-red-600"
        bgColor="bg-red-50"
      />
      <StatCard
        title="数量增加"
        value={stats.increased}
        icon={<TrendingUp className="h-6 w-6" />}
        color="text-green-600"
        bgColor="bg-green-50"
      />
      <StatCard
        title="数量减少"
        value={stats.decreased}
        icon={<TrendingDown className="h-6 w-6" />}
        color="text-red-600"
        bgColor="bg-red-50"
      />
      <StatCard
        title="现金标志变化"
        value={stats.cashFlagChanged}
        icon={<AlertTriangle className="h-6 w-6" />}
        color="text-orange-600"
        bgColor="bg-orange-50"
      />
      <StatCard
        title="权重变化"
        value={stats.ratioChanged}
        icon={<AlertTriangle className="h-6 w-6" />}
        color="text-purple-600"
        bgColor="bg-purple-50"
      />
    </div>
  )
}

export default SummaryStats
