import { TrendingUp, TrendingDown, Plus, Minus, AlertTriangle } from 'lucide-react'

const SummaryStats = ({ data }) => {
  const stats = {
    total: data.length,
    changed: data.filter(item => item.quantityDiff !== 0 || item.cashFlagChanged || item.ratioChanged).length,
    new: data.filter(item => item.isNew).length,
    removed: data.filter(item => item.isRemoved).length,
    increased: data.filter(item => item.quantityDiff > 0).length,
    decreased: data.filter(item => item.quantityDiff < 0).length,
    cashFlagChanged: data.filter(item => item.cashFlagChanged).length,
    ratioChanged: data.filter(item => item.ratioChanged).length
  }

  const StatCard = ({ title, value, icon, colorClass }) => (
    <div className={`stat-card ${colorClass}`}>
      <div>
        <p className="stat-title">{title}</p>
        <p className="stat-value">{value}</p>
      </div>
      <div>
        {icon}
      </div>
    </div>
  )

  return (
    <div className="grid grid-cols-8" style={{ marginBottom: '1.5rem' }}>
      <StatCard
        title="总数"
        value={stats.total}
        icon={<TrendingUp className="icon-lg" />}
        colorClass="blue"
      />
      <StatCard
        title="有变化"
        value={stats.changed}
        icon={<AlertTriangle className="icon-lg" />}
        colorClass="yellow"
      />
      <StatCard
        title="新增"
        value={stats.new}
        icon={<Plus className="icon-lg" />}
        colorClass="green"
      />
      <StatCard
        title="移除"
        value={stats.removed}
        icon={<Minus className="icon-lg" />}
        colorClass="red"
      />
      <StatCard
        title="数量增加"
        value={stats.increased}
        icon={<TrendingUp className="icon-lg" />}
        colorClass="green"
      />
      <StatCard
        title="数量减少"
        value={stats.decreased}
        icon={<TrendingDown className="icon-lg" />}
        colorClass="red"
      />
      <StatCard
        title="现金标志变化"
        value={stats.cashFlagChanged}
        icon={<AlertTriangle className="icon-lg" />}
        colorClass="orange"
      />
      <StatCard
        title="权重变化"
        value={stats.ratioChanged}
        icon={<AlertTriangle className="icon-lg" />}
        colorClass="purple"
      />
    </div>
  )
}

export default SummaryStats
