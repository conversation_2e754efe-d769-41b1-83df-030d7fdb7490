/* 基础样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #000000;
  color: #d1d5db;
}

#root {
  min-height: 100vh;
}

/* 容器样式 */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* 页面标题 */
.page-header {
  margin-bottom: 2rem;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  font-size: 1.875rem;
  font-weight: 600;
  color: #ffffff;
}

.page-subtitle {
  color: #9ca3af;
  margin: 0;
}

/* 卡片样式 */
.card {
  background: #111111;
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  border: 1px solid #2a2a2a;
  margin-bottom: 2rem;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #2a2a2a;
}

.card-content {
  padding: 1.5rem;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid-cols-8 {
  grid-template-columns: repeat(8, 1fr);
}

@media (max-width: 768px) {
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-8 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-8 {
    grid-template-columns: 1fr;
  }
}

/* 表单样式 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #d1d5db;
}

.form-input,
.form-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #333333;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background-color: #1a1a1a;
  color: #ffffff;
  transition: all 0.2s ease-in-out;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #666666;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.btn-primary {
  background-color: #ffffff;
  color: #000000;
  font-weight: 500;
}

.btn-primary:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.btn-primary:disabled {
  background-color: #333333;
  color: #666666;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #1a1a1a;
  color: #d1d5db;
  border: 1px solid #333333;
}

.btn-secondary:hover {
  background-color: #2a2a2a;
}

/* 统计卡片 */
.stat-card {
  padding: 1.25rem;
  border-radius: 0.75rem;
  border: 1px solid #2a2a2a;
  background-color: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease-in-out;
}

.stat-card:hover {
  border-color: #404040;
  background-color: #1f1f1f;
}

.stat-card.blue {
  color: #ffffff;
}

.stat-card.yellow {
  color: #ffffff;
}

.stat-card.green {
  color: #ffffff;
}

.stat-card.red {
  color: #ffffff;
}

.stat-card.orange {
  color: #ffffff;
}

.stat-card.purple {
  color: #ffffff;
}

.stat-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #9ca3af;
  margin: 0 0 0.5rem 0;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0;
}

/* 筛选按钮 */
.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.filter-btn.active {
  background-color: #ffffff;
  color: #000000;
  font-weight: 500;
}

.filter-btn.inactive {
  background-color: #1a1a1a;
  color: #9ca3af;
  border: 1px solid #333333;
}

.filter-btn.inactive:hover {
  background-color: #2a2a2a;
  color: #d1d5db;
}

.filter-btn.all.active,
.filter-btn.changed.active,
.filter-btn.new.active,
.filter-btn.removed.active {
  background-color: #ffffff;
  color: #000000;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
}

.table {
  min-width: 100%;
  border-collapse: collapse;
  background: #111111;
}

.table th {
  padding: 1rem 1.5rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 600;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background-color: #0a0a0a;
  border-bottom: 1px solid #2a2a2a;
}

.table th.sortable {
  cursor: pointer;
  user-select: none;
}

.table th.sortable:hover {
  background-color: #1a1a1a;
  color: #d1d5db;
}

.table td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #2a2a2a;
  font-size: 0.875rem;
  color: #d1d5db;
}

.table tr.new {
  background-color: #1a1a1a;
  border-left: 3px solid #ffffff;
}

.table tr.removed {
  background-color: #1a1a1a;
  border-left: 3px solid #666666;
}

.table tr.changed {
  background-color: #1a1a1a;
  border-left: 3px solid #cccccc;
}

.table tr.normal {
  background-color: #111111;
}

/* 状态标签 */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.new {
  background-color: #2a2a2a;
  color: #ffffff;
  border: 1px solid #404040;
}

.status-badge.removed {
  background-color: #2a2a2a;
  color: #9ca3af;
  border: 1px solid #404040;
}

.status-badge.changed {
  background-color: #2a2a2a;
  color: #ffffff;
  border: 1px solid #404040;
}

.status-badge.bond-type {
  background-color: #1a1a1a;
  color: #9ca3af;
  border: 1px solid #333333;
}

/* 差异显示 */
.diff-container {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.diff-container.positive {
  background-color: #2a2a2a;
  color: #ffffff;
  border: 1px solid #404040;
}

.diff-container.negative {
  background-color: #2a2a2a;
  color: #ffffff;
  border: 1px solid #404040;
}

.diff-container.neutral {
  background-color: #1a1a1a;
  color: #9ca3af;
  border: 1px solid #333333;
}

/* 比较字段 */
.compare-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.compare-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.compare-label {
  color: #9ca3af;
  min-width: 3rem;
}

.compare-value {
  color: #e5e7eb;
}

.compare-value.old {
  text-decoration: line-through;
  color: #6b7280;
}

.compare-value.new {
  font-weight: 600;
  color: #ffffff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
  font-size: 1.125rem;
}

/* 图标样式 */
.icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
  color: #9ca3af;
}

.icon-lg {
  width: 1.5rem;
  height: 1.5rem;
  color: inherit;
}

.icon-xl {
  width: 2rem;
  height: 2rem;
  color: inherit;
}

/* 工具提示 */
.tooltip {
  position: relative;
  display: inline-block;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #111111;
}

::-webkit-scrollbar-thumb {
  background: #333333;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #404040;
}

/* 选择文本样式 */
::selection {
  background-color: #ffffff;
  color: #000000;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .table th,
  .table td {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .filter-buttons {
    justify-content: center;
  }
}
