/* 基础样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0f0f0f;
  color: #e5e7eb;
}

#root {
  min-height: 100vh;
}

/* 容器样式 */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* 页面标题 */
.page-header {
  margin-bottom: 2rem;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  font-size: 1.875rem;
  font-weight: bold;
  color: #f9fafb;
}

.page-subtitle {
  color: #9ca3af;
  margin: 0;
}

/* 卡片样式 */
.card {
  background: #1f2937;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
  border: 1px solid #374151;
  margin-bottom: 2rem;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #374151;
}

.card-content {
  padding: 1.5rem;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid-cols-8 {
  grid-template-columns: repeat(8, 1fr);
}

@media (max-width: 768px) {
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-8 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-8 {
    grid-template-columns: 1fr;
  }
}

/* 表单样式 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #d1d5db;
}

.form-input,
.form-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #4b5563;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: #374151;
  color: #f9fafb;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #1d4ed8;
}

.btn-primary:disabled {
  background-color: #6b7280;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #374151;
  color: #e5e7eb;
  border: 1px solid #4b5563;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

/* 统计卡片 */
.stat-card {
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #4b5563;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-card.blue {
  background-color: #1e3a8a;
  color: #93c5fd;
}

.stat-card.yellow {
  background-color: #92400e;
  color: #fbbf24;
}

.stat-card.green {
  background-color: #14532d;
  color: #4ade80;
}

.stat-card.red {
  background-color: #7f1d1d;
  color: #f87171;
}

.stat-card.orange {
  background-color: #9a3412;
  color: #fb923c;
}

.stat-card.purple {
  background-color: #581c87;
  color: #c084fc;
}

.stat-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #9ca3af;
  margin: 0 0 0.25rem 0;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0;
}

/* 筛选按钮 */
.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.filter-btn.active {
  color: white;
}

.filter-btn.inactive {
  background-color: #374151;
  color: #d1d5db;
  border: 1px solid #4b5563;
}

.filter-btn.inactive:hover {
  background-color: #4b5563;
}

.filter-btn.all.active {
  background-color: #2563eb;
}

.filter-btn.changed.active {
  background-color: #d97706;
}

.filter-btn.new.active {
  background-color: #16a34a;
}

.filter-btn.removed.active {
  background-color: #dc2626;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
}

.table {
  min-width: 100%;
  border-collapse: collapse;
  background: #1f2937;
}

.table th {
  padding: 0.75rem 1.5rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background-color: #111827;
  border-bottom: 1px solid #374151;
}

.table th.sortable {
  cursor: pointer;
  user-select: none;
}

.table th.sortable:hover {
  background-color: #1f2937;
}

.table td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #374151;
  font-size: 0.875rem;
  color: #e5e7eb;
}

.table tr.new {
  background-color: #14532d;
  border-left: 4px solid #16a34a;
}

.table tr.removed {
  background-color: #7f1d1d;
  border-left: 4px solid #dc2626;
}

.table tr.changed {
  background-color: #92400e;
  border-left: 4px solid #d97706;
}

.table tr.normal {
  background-color: #1f2937;
}

/* 状态标签 */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.625rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.new {
  background-color: #14532d;
  color: #4ade80;
}

.status-badge.removed {
  background-color: #7f1d1d;
  color: #f87171;
}

.status-badge.changed {
  background-color: #92400e;
  color: #fbbf24;
}

.status-badge.bond-type {
  background-color: #374151;
  color: #d1d5db;
}

/* 差异显示 */
.diff-container {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.diff-container.positive {
  background-color: #14532d;
  color: #4ade80;
}

.diff-container.negative {
  background-color: #7f1d1d;
  color: #f87171;
}

.diff-container.neutral {
  background-color: #374151;
  color: #9ca3af;
}

/* 比较字段 */
.compare-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.compare-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.compare-label {
  color: #9ca3af;
  min-width: 3rem;
}

.compare-value {
  color: #e5e7eb;
}

.compare-value.old {
  text-decoration: line-through;
  color: #6b7280;
}

.compare-value.new {
  font-weight: 500;
  color: #60a5fa;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
  font-size: 1.125rem;
}

/* 图标样式 */
.icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
  color: #9ca3af;
}

.icon-lg {
  width: 1.5rem;
  height: 1.5rem;
  color: inherit;
}

.icon-xl {
  width: 2rem;
  height: 2rem;
  color: inherit;
}

/* 工具提示 */
.tooltip {
  position: relative;
  display: inline-block;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 选择文本样式 */
::selection {
  background-color: #2563eb;
  color: white;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .table th,
  .table td {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .filter-buttons {
    justify-content: center;
  }
}
