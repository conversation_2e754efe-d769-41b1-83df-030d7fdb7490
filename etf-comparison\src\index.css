/* 基础样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
  color: #1f2937;
}

#root {
  min-height: 100vh;
}

/* 容器样式 */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* 页面标题 */
.page-header {
  margin-bottom: 2rem;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  font-size: 1.875rem;
  font-weight: bold;
  color: #1f2937;
}

.page-subtitle {
  color: #6b7280;
  margin: 0;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 2rem;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.card-content {
  padding: 1.5rem;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid-cols-8 {
  grid-template-columns: repeat(8, 1fr);
}

@media (max-width: 768px) {
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-8 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-8 {
    grid-template-columns: 1fr;
  }
}

/* 表单样式 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-primary:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background-color: #f9fafb;
}

/* 统计卡片 */
.stat-card {
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-card.blue {
  background-color: #eff6ff;
  color: #2563eb;
}

.stat-card.yellow {
  background-color: #fffbeb;
  color: #d97706;
}

.stat-card.green {
  background-color: #f0fdf4;
  color: #16a34a;
}

.stat-card.red {
  background-color: #fef2f2;
  color: #dc2626;
}

.stat-card.orange {
  background-color: #fff7ed;
  color: #ea580c;
}

.stat-card.purple {
  background-color: #faf5ff;
  color: #9333ea;
}

.stat-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0 0.25rem 0;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0;
}

/* 筛选按钮 */
.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.filter-btn.active {
  color: white;
}

.filter-btn.inactive {
  background-color: #f3f4f6;
  color: #374151;
}

.filter-btn.inactive:hover {
  background-color: #e5e7eb;
}

.filter-btn.all.active {
  background-color: #3b82f6;
}

.filter-btn.changed.active {
  background-color: #d97706;
}

.filter-btn.new.active {
  background-color: #16a34a;
}

.filter-btn.removed.active {
  background-color: #dc2626;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
}

.table {
  min-width: 100%;
  border-collapse: collapse;
  background: white;
}

.table th {
  padding: 0.75rem 1.5rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.table th.sortable {
  cursor: pointer;
  user-select: none;
}

.table th.sortable:hover {
  background-color: #f3f4f6;
}

.table td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.table tr.new {
  background-color: #f0fdf4;
  border-left: 4px solid #16a34a;
}

.table tr.removed {
  background-color: #fef2f2;
  border-left: 4px solid #dc2626;
}

.table tr.changed {
  background-color: #fffbeb;
  border-left: 4px solid #d97706;
}

.table tr.normal {
  background-color: white;
}

/* 状态标签 */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.625rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.new {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.removed {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-badge.changed {
  background-color: #fef3c7;
  color: #92400e;
}

.status-badge.bond-type {
  background-color: #f3f4f6;
  color: #374151;
}

/* 差异显示 */
.diff-container {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.diff-container.positive {
  background-color: #dcfce7;
  color: #166534;
}

.diff-container.negative {
  background-color: #fee2e2;
  color: #991b1b;
}

.diff-container.neutral {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* 比较字段 */
.compare-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.compare-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.compare-label {
  color: #6b7280;
  min-width: 3rem;
}

.compare-value {
  color: #1f2937;
}

.compare-value.old {
  text-decoration: line-through;
  color: #9ca3af;
}

.compare-value.new {
  font-weight: 500;
  color: #2563eb;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: #9ca3af;
  font-size: 1.125rem;
}

/* 图标样式 */
.icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.icon-lg {
  width: 1.5rem;
  height: 1.5rem;
}

.icon-xl {
  width: 2rem;
  height: 2rem;
}

/* 工具提示 */
.tooltip {
  position: relative;
  display: inline-block;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .table th,
  .table td {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .filter-buttons {
    justify-content: center;
  }
}
