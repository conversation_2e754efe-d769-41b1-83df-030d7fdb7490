/* 基础样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #000000;
  color: #ffffff;
  line-height: 1.6;
}

#root {
  min-height: 100vh;
}

/* 容器样式 */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 3rem 2rem;
}

/* 页面标题 */
.page-header {
  margin-bottom: 3rem;
  text-align: center;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: -0.025em;
}

.page-subtitle {
  color: #ffffff;
  opacity: 0.7;
  margin: 0;
  font-size: 1.125rem;
  font-weight: 400;
}

/* 卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 3rem;
  overflow: hidden;
}

.card-header {
  padding: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-content {
  padding: 2rem;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 2rem;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid-cols-8 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 1024px) {
  .grid-cols-8 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-8 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-8 {
    grid-template-columns: 1fr;
  }
}

/* 表单样式 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 0.75rem;
}

.form-input,
.form-select {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  font-size: 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  transition: all 0.3s ease-in-out;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #ff7833;
  box-shadow: 0 0 0 3px rgba(255, 120, 51, 0.2);
  background-color: rgba(255, 255, 255, 0.08);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2.5rem;
  border: none;
  border-radius: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  text-decoration: none;
  letter-spacing: 0.025em;
}

.btn-primary {
  background: linear-gradient(135deg, #ff7833 0%, #ff3446 100%);
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(255, 120, 51, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 120, 51, 0.4);
}

.btn-primary:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background-color: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

/* 统计卡片 */
.stat-card {
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.03);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease-in-out;
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  border-color: rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.stat-card.blue,
.stat-card.yellow,
.stat-card.green,
.stat-card.red,
.stat-card.orange,
.stat-card.purple {
  color: #ffffff;
}

.stat-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: #ffffff;
}

/* 筛选按钮 */
.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.filter-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  letter-spacing: 0.025em;
}

.filter-btn.active {
  background-color: #ffffff;
  color: #000000;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.filter-btn.inactive {
  background-color: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-btn.inactive:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.2);
}

.filter-btn.all.active,
.filter-btn.changed.active,
.filter-btn.new.active,
.filter-btn.removed.active {
  background-color: #ffffff;
  color: #000000;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
}

.table {
  min-width: 100%;
  border-collapse: collapse;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 1rem;
  overflow: hidden;
}

.table th {
  padding: 1.25rem 1.5rem;
  text-align: left;
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease-in-out;
}

.table th.sortable:hover {
  background: rgba(255, 255, 255, 0.08);
  color: #ffffff;
}

.table td {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  font-size: 0.875rem;
  color: #ffffff;
}

.table tr.new {
  background: rgba(0, 224, 86, 0.1);
  border-left: 4px solid #00e056;
}

.table tr.removed {
  background: rgba(126, 14, 28, 0.1);
  border-left: 4px solid #7e0e1c;
}

.table tr.changed {
  background: rgba(255, 120, 51, 0.1);
  border-left: 4px solid #ff7833;
}

.table tr.normal {
  background: rgba(255, 255, 255, 0.02);
}

/* 状态标签 */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 1rem;
  border-radius: 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.status-badge.new {
  background: rgba(0, 224, 86, 0.15);
  color: #00e056;
  border: 1px solid rgba(0, 224, 86, 0.3);
}

.status-badge.removed {
  background: rgba(126, 14, 28, 0.15);
  color: #ff3446;
  border: 1px solid rgba(126, 14, 28, 0.3);
}

.status-badge.changed {
  background: rgba(255, 120, 51, 0.15);
  color: #ff7833;
  border: 1px solid rgba(255, 120, 51, 0.3);
}

.status-badge.bond-type {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 差异显示 */
.diff-container {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.diff-container.positive {
  background: rgba(0, 224, 86, 0.15);
  color: #00e056;
  border: 1px solid rgba(0, 224, 86, 0.3);
}

.diff-container.negative {
  background: rgba(255, 52, 70, 0.15);
  color: #ff3446;
  border: 1px solid rgba(255, 52, 70, 0.3);
}

.diff-container.neutral {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 比较字段 */
.compare-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.compare-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.compare-label {
  color: rgba(255, 255, 255, 0.6);
  min-width: 3rem;
  font-size: 0.875rem;
}

.compare-value {
  color: #ffffff;
}

.compare-value.old {
  text-decoration: line-through;
  color: rgba(255, 255, 255, 0.4);
}

.compare-value.new {
  font-weight: 600;
  color: #fdd908;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 4rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.125rem;
}

/* 图标样式 */
.icon {
  width: 1.125rem;
  height: 1.125rem;
  flex-shrink: 0;
  color: rgba(255, 255, 255, 0.7);
}

.icon-lg {
  width: 1.5rem;
  height: 1.5rem;
  color: inherit;
}

.icon-xl {
  width: 2.5rem;
  height: 2.5rem;
  color: inherit;
}

/* 工具提示 */
.tooltip {
  position: relative;
  display: inline-block;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 选择文本样式 */
::selection {
  background-color: #ff7833;
  color: #ffffff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .container {
    padding: 2rem 1rem;
  }

  .page-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .table th,
  .table td {
    padding: 1rem 0.75rem;
    font-size: 0.8rem;
  }

  .filter-buttons {
    justify-content: center;
    gap: 0.75rem;
  }

  .btn {
    padding: 0.875rem 2rem;
    font-size: 0.875rem;
  }

  .stat-card {
    padding: 1.25rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }
}
